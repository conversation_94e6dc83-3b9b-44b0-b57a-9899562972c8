import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current working directory.
  const env = loadEnv(mode, process.cwd(), "");

  return {
    plugins: [
      react({
        include: "**/*.{jsx,js}",
      }),
    ],
    define: {
      // Make env variables available as process.env.VARIABLE_NAME
      "process.env.VITE_API_BASE_URL": JSON.stringify(env.VITE_API_BASE_URL),
      "process.env.VITE_WS_BASE_URL": JSON.stringify(env.VITE_WS_BASE_URL),
      // Add more environment variables as needed
    },
    esbuild: {
      loader: "jsx",
      include: /src\/.*\.[jt]sx?$/,
      exclude: [],
      drop: mode === 'production' ? ['console', 'debugger'] : [],
    },
    optimizeDeps: {
      esbuildOptions: {
        loader: {
          ".js": "jsx",
        },
      },
    },
    server: {
      port: 3000,
      open: true,
    },
    build: {
      outDir: "build",
      sourcemap: true,
    },
    css: {
      preprocessorOptions: {
        scss: {
          // additionalData: `@import "src/styles/variables.scss";`
        },
        less: {
          javascriptEnabled: true,
          modifyVars: {
            // Ant Design theme variables can be customized here
          },
        },
      },
    },
    resolve: {
      alias: {
        "@": "/src",
      },
    },
  };
});
