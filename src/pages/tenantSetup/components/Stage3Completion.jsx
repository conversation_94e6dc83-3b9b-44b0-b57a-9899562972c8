import { Row, Col, Typography, Space, Card, Tag, Spin } from "antd";
import {
  CheckCircleOutlined,
  BankOutlined,
  RobotOutlined,
  ThunderboltOutlined,
} from "@ant-design/icons";
import { useAppDispatch } from "../../../hooks/reduxHooks";
import {
  getBusinessInfoApi,
  getTenantOrgTypesApi,
  getTenantToolsApi,
} from "../../../services/tenantSetup.service";
import { useEffect, useState } from "react";

const { Title, Text, Paragraph } = Typography;

const Stage3Completion = () => {
  const dispatch = useAppDispatch();
  const [businessInfo, setBusinessInfo] = useState(null);
  const [organizationTypes, setOrganizationTypes] = useState([]);
  const [toolsList, setToolsList] = useState([]);
  const [loading, setLoading] = useState(true);

  const getBusinessTypeLabel = (value) => {
    // Find the organization type in the API data
    const orgType = organizationTypes.find(type => type.name === value);
    return orgType ? orgType.label : value;
  };

  const getToolLabel = (value) => {
    // Find the tool in the API data
    const tool = toolsList.find(tool => tool.name === value);
    return tool ? tool.label : value;
  };

  const getResponseStyleLabel = (style) => {
    const styles = {
      simplified: "Simplified",
      detailed: "Detailed",
      elaborated: "Elaborated",
    };
    return styles[style] || style;
  };

  const getResponseStyleDescription = (style) => {
    const descriptions = {
      simplified: "Clear, concise answers",
      detailed: "Comprehensive explanations",
      elaborated: "Technical details with step-by-step guidance",
    };
    return descriptions[style] || "AI response style";
  };

  const formatPhoneNumber = (businessInfo) => {
    if (businessInfo?.org_contact) {
      return businessInfo.org_contact;
    }
    if (businessInfo?.country_code && businessInfo?.phone_number) {
      return `${businessInfo.country_code} ${businessInfo.phone_number}`;
    }
    return "Not provided";
  };

  const getAgentGoalSummary = (goalType) => {
    // Find the prompt summary from organization types data
    for (const orgType of organizationTypes) {
      if (orgType.agent_goals) {
        const goal = orgType.agent_goals.find(
          (g) => g.prompt_type === goalType
        );
        if (goal) {
          return goal.prompt_summary;
        }
      }
    }
    return goalType; // Fallback to the goal type itself if not found
  };

  useEffect(() => {
    setLoading(true);

    // Fetch business info, organization types, and tools
    Promise.all([
      dispatch(
        getBusinessInfoApi({
          finalCallback: () => {},
          successCallback: (response) => {
            setBusinessInfo(response);
          },
          failureCallback: () => {
            // Failed to fetch business info
          },
        })
      ),
      dispatch(
        getTenantOrgTypesApi({
          finalCallback: () => {},
          successCallback: (response) => {
            setOrganizationTypes(response);
          },
          failureCallback: () => {
            // Failed to fetch organization types
          },
        })
      ),
      dispatch(
        getTenantToolsApi({
          finalCallback: () => {},
          successCallback: (response) => {
            setToolsList(response);
          },
          failureCallback: () => {
            // Failed to fetch tools
          },
        })
      ),
    ]).finally(() => {
      setLoading(false);
    });
  }, [dispatch]);

  return (
    <div className="stage3-completion">
      <div
        className="stage3-completion__header"
        style={{ textAlign: "center", marginBottom: 40 }}
      >
        <div
          style={{
            background: "#52c41a",
            borderRadius: "50%",
            width: 80,
            height: 80,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            margin: "0 auto 24px auto",
          }}
        >
          <iconify-icon
            icon="mingcute:check-2-line"
            width="40"
            height="40"
            style={{ color: "white" }}
          />
        </div>

        <Title level={2} style={{ color: "#262626", marginBottom: 12 }}>
          Review & Confirm Setup
        </Title>

        <Paragraph
          style={{
            fontSize: "16px",
            color: "#595959",
            marginBottom: 0,
            maxWidth: 500,
            margin: "0 auto",
          }}
        >
          Review your configuration and confirm to complete the setup.
        </Paragraph>
      </div>

      {loading ? (
        <div style={{ textAlign: "center", padding: "60px 0" }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>
            <Text type="secondary">Loading your setup information...</Text>
          </div>
        </div>
      ) : (
        <>
          {/* Setup Summary */}
          <Row gutter={[24, 24]} style={{ marginBottom: 32 }}>
            {/* Organization Information */}
            <Col xs={24} lg={12}>
              <Card
                title={
                  <Space>
                    <BankOutlined style={{ color: "#595959", fontSize: 16 }} />
                    <Text strong style={{ fontSize: 16 }}>
                      Organization Information
                    </Text>
                  </Space>
                }
                className="summary-card"
                style={{
                  height: "100%",
                  borderRadius: "8px",
                  border: "1px solid #f0f0f0",
                }}
              >
                <div style={{ padding: "20px" }}>
                  <Space
                    direction="vertical"
                    size={8}
                    style={{ width: "100%" }}
                  >
                    <div
                      style={{
                        marginBottom: 10,
                        display: "flex",
                        alignItems: "flex-start",
                        gap: 12,
                      }}
                    >
                      <div
                        style={{
                          width: 6,
                          height: 6,
                          borderRadius: "50%",
                          background: "#262626",
                          marginTop: 6,
                          flexShrink: 0,
                        }}
                      />
                      <div style={{ flex: 1 }}>
                        <Text
                          strong
                          style={{
                            fontSize: 14,
                            fontWeight: 600,
                            color: "#262626",
                            display: "block",
                            marginBottom: 4,
                          }}
                        >
                          Organization Name
                        </Text>
                        <Text style={{ fontSize: 14, color: "#595959" }}>
                          {businessInfo?.org_name || "Not provided"}
                        </Text>
                      </div>
                    </div>

                    <div
                      style={{
                        marginBottom: 10,
                        display: "flex",
                        alignItems: "flex-start",
                        gap: 12,
                      }}
                    >
                      <div
                        style={{
                          width: 6,
                          height: 6,
                          borderRadius: "50%",
                          background: "#262626",
                          marginTop: 6,
                          flexShrink: 0,
                        }}
                      />
                      <div style={{ flex: 1 }}>
                        <Text
                          strong
                          style={{
                            fontSize: 14,
                            fontWeight: 600,
                            color: "#262626",
                            display: "block",
                            marginBottom: 4,
                          }}
                        >
                          Organization Type
                        </Text>
                        <Tag
                          style={{
                            borderRadius: 6,
                            border: "1px solid #d9d9d9",
                            background: "#fafafa",
                            fontSize: 13,
                            padding: "4px 12px",
                          }}
                        >
                          {getBusinessTypeLabel(businessInfo?.org_type) ||
                            "Not selected"}
                        </Tag>
                      </div>
                    </div>

                    <div
                      style={{
                        marginBottom: 10,
                        display: "flex",
                        alignItems: "flex-start",
                        gap: 12,
                      }}
                    >
                      <div
                        style={{
                          width: 6,
                          height: 6,
                          borderRadius: "50%",
                          background: "#262626",
                          marginTop: 6,
                          flexShrink: 0,
                        }}
                      />
                      <div style={{ flex: 1 }}>
                        <Text
                          strong
                          style={{
                            fontSize: 14,
                            fontWeight: 600,
                            color: "#262626",
                            display: "block",
                            marginBottom: 4,
                          }}
                        >
                          Contact Number
                        </Text>
                        <Text style={{ fontSize: 14, color: "#595959" }}>
                          {formatPhoneNumber(businessInfo)}
                        </Text>
                      </div>
                    </div>

                    <div
                      style={{
                        marginBottom: 10,
                        display: "flex",
                        alignItems: "flex-start",
                        gap: 12,
                      }}
                    >
                      <div
                        style={{
                          width: 6,
                          height: 6,
                          borderRadius: "50%",
                          background: "#262626",
                          marginTop: 6,
                          flexShrink: 0,
                        }}
                      />
                      <div style={{ flex: 1 }}>
                        <Text
                          strong
                          style={{
                            fontSize: 14,
                            fontWeight: 600,
                            color: "#262626",
                            display: "block",
                            marginBottom: 4,
                          }}
                        >
                          Email Address
                        </Text>
                        <Text style={{ fontSize: 14, color: "#595959" }}>
                          {businessInfo?.org_email || "Not provided"}
                        </Text>
                      </div>
                    </div>

                    <div
                      style={{
                        marginBottom: 0,
                        display: "flex",
                        alignItems: "flex-start",
                        gap: 12,
                      }}
                    >
                      <div
                        style={{
                          width: 6,
                          height: 6,
                          borderRadius: "50%",
                          background: "#262626",
                          marginTop: 6,
                          flexShrink: 0,
                        }}
                      />
                      <div style={{ flex: 1 }}>
                        <Text
                          strong
                          style={{
                            fontSize: 14,
                            fontWeight: 600,
                            color: "#262626",
                            display: "block",
                            marginBottom: 4,
                          }}
                        >
                          Description
                        </Text>
                        <Text
                          style={{
                            fontSize: 14,
                            lineHeight: 1.6,
                            color: "#595959",
                          }}
                        >
                          {businessInfo?.org_description || "Not provided"}
                        </Text>
                      </div>
                    </div>
                  </Space>
                </div>
              </Card>
            </Col>

            {/* Agent Setup */}
            <Col xs={24} lg={12}>
              <Card
                title={
                  <Space>
                    <RobotOutlined style={{ color: "#595959", fontSize: 16 }} />
                    <Text strong style={{ fontSize: 16 }}>
                      AI Agent Setup
                    </Text>
                  </Space>
                }
                className="summary-card"
                style={{
                  height: "100%",
                  borderRadius: "8px",
                  border: "1px solid #f0f0f0",
                }}
              >
                <div style={{ padding: "20px" }}>
                  <Space
                    direction="vertical"
                    size={8}
                    style={{ width: "100%" }}
                  >
                    <div
                      style={{
                        marginBottom: 10,
                        display: "flex",
                        alignItems: "flex-start",
                        gap: 12,
                      }}
                    >
                      <div
                        style={{
                          width: 6,
                          height: 6,
                          borderRadius: "50%",
                          background: "#262626",
                          marginTop: 6,
                          flexShrink: 0,
                        }}
                      />
                      <div style={{ flex: 1 }}>
                        <Text
                          strong
                          style={{
                            fontSize: 14,
                            fontWeight: 600,
                            color: "#262626",
                            display: "block",
                            marginBottom: 4,
                          }}
                        >
                          Agent Name
                        </Text>
                        <Text style={{ fontSize: 14, color: "#595959" }}>
                          {businessInfo?.agent_name || "AI Bot"}
                        </Text>
                      </div>
                    </div>

                    <div
                      style={{
                        marginBottom: 10,
                        display: "flex",
                        alignItems: "flex-start",
                        gap: 12,
                      }}
                    >
                      <div
                        style={{
                          width: 6,
                          height: 6,
                          borderRadius: "50%",
                          background: "#262626",
                          marginTop: 6,
                          flexShrink: 0,
                        }}
                      />
                      <div style={{ flex: 1 }}>
                        <Text
                          strong
                          style={{
                            fontSize: 14,
                            fontWeight: 600,
                            color: "#262626",
                            display: "block",
                            marginBottom: 4,
                          }}
                        >
                          Language
                        </Text>
                        <Space wrap>
                          {businessInfo?.language?.length > 0 ? (
                            businessInfo.language.map((lang, index) => (
                              <Tag
                                key={lang}
                                color={index === 0 && lang !== "Auto Detect" ? 'blue' : 'default'}
                                style={{
                                  borderRadius: 6,
                                  fontSize: 13,
                                  padding: "4px 12px",
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 4,
                                }}
                              >
                                {lang}
                                {index === 0 && lang !== "Auto Detect" && (
                                  <span style={{ fontSize: 11, opacity: 0.8 }}>
                                    (Primary)
                                  </span>
                                )}
                              </Tag>
                            ))
                          ) : (
                            <Text style={{ color: "#8c8c8c" }}>
                              Not selected
                            </Text>
                          )}
                        </Space>
                      </div>
                    </div>

                    <div
                      style={{
                        marginBottom: 10,
                        display: "flex",
                        alignItems: "flex-start",
                        gap: 12,
                      }}
                    >
                      <div
                        style={{
                          width: 6,
                          height: 6,
                          borderRadius: "50%",
                          background: "#262626",
                          marginTop: 6,
                          flexShrink: 0,
                        }}
                      />
                      <div style={{ flex: 1 }}>
                        <Text
                          strong
                          style={{
                            fontSize: 14,
                            fontWeight: 600,
                            color: "#262626",
                            display: "block",
                            marginBottom: 4,
                          }}
                        >
                          Agent Goal
                        </Text>
                        <div>
                          {businessInfo?.agent_goal_type ? (
                            <Text
                              style={{
                                fontSize: 14,
                                lineHeight: 1.6,
                                color: "#595959",
                                display: "block",
                              }}
                            >
                              {getAgentGoalSummary(
                                businessInfo.agent_goal_type
                              )}
                            </Text>
                          ) : (
                            <Text style={{ color: "#8c8c8c" }}>
                              Not specified
                            </Text>
                          )}
                        </div>
                      </div>
                    </div>

                    {businessInfo?.additional_agent_goal && (
                      <div
                        style={{
                          marginBottom: 0,
                          display: "flex",
                          alignItems: "flex-start",
                          gap: 12,
                        }}
                      >
                        <div
                          style={{
                            width: 6,
                            height: 6,
                            borderRadius: "50%",
                            background: "#262626",
                            marginTop: 6,
                            flexShrink: 0,
                          }}
                        />
                        <div style={{ flex: 1 }}>
                          <Text
                            strong
                            style={{
                              fontSize: 14,
                              fontWeight: 600,
                              color: "#262626",
                              display: "block",
                              marginBottom: 4,
                            }}
                          >
                            Additional Agent Instructions
                          </Text>
                          <Text
                            style={{
                              fontSize: 14,
                              lineHeight: 1.6,
                              color: "#595959",
                            }}
                          >
                            {businessInfo.additional_agent_goal}
                          </Text>
                        </div>
                      </div>
                    )}
                  </Space>
                </div>
              </Card>
            </Col>

            {/* AI Configuration */}
            <Col xs={24} lg={12}>
              <Card
                title={
                  <Space>
                    <ThunderboltOutlined
                      style={{ color: "#595959", fontSize: 16 }}
                    />
                    <Text strong style={{ fontSize: 16 }}>
                      AI Configuration
                    </Text>
                  </Space>
                }
                className="summary-card"
                style={{
                  height: "100%",
                  borderRadius: "8px",
                  border: "1px solid #f0f0f0",
                }}
              >
                <div style={{ padding: "20px" }}>
                  <Space
                    direction="vertical"
                    size={8}
                    style={{ width: "100%" }}
                  >
                    <div
                      style={{
                        marginBottom: 10,
                        display: "flex",
                        alignItems: "flex-start",
                        gap: 12,
                      }}
                    >
                      <div
                        style={{
                          width: 6,
                          height: 6,
                          borderRadius: "50%",
                          background: "#262626",
                          marginTop: 6,
                          flexShrink: 0,
                        }}
                      />
                      <div style={{ flex: 1 }}>
                        <Text
                          strong
                          style={{
                            fontSize: 14,
                            fontWeight: 600,
                            color: "#262626",
                            display: "block",
                            marginBottom: 4,
                          }}
                        >
                          Response Style
                        </Text>
                        <div>
                          <Tag
                            style={{
                              borderRadius: 6,
                              border: "1px solid #d9d9d9",
                              background: "#fafafa",
                              fontSize: 13,
                              padding: "4px 12px",
                            }}
                          >
                            {getResponseStyleLabel(
                              businessInfo?.preferred_prompt
                            ) || "Not selected"}
                          </Tag>
                          {businessInfo?.preferred_prompt && (
                            <div style={{ marginTop: 6 }}>
                              <Text style={{ fontSize: 13, color: "#8c8c8c" }}>
                                {getResponseStyleDescription(
                                  businessInfo.preferred_prompt
                                )}
                              </Text>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    <div
                      style={{
                        marginBottom: 10,
                        display: "flex",
                        alignItems: "flex-start",
                        gap: 12,
                      }}
                    >
                      <div
                        style={{
                          width: 6,
                          height: 6,
                          borderRadius: "50%",
                          background: "#262626",
                          marginTop: 6,
                          flexShrink: 0,
                        }}
                      />
                      <div style={{ flex: 1 }}>
                        <Text
                          strong
                          style={{
                            fontSize: 14,
                            fontWeight: 600,
                            color: "#262626",
                            display: "block",
                            marginBottom: 4,
                          }}
                        >
                          Ticket Tools
                        </Text>
                        <Space wrap style={{ marginBottom: 12 }}>
                          {businessInfo?.used_tools?.length > 0 ? (
                            businessInfo.used_tools.map((tool) => (
                              <Tag
                                key={tool}
                                style={{
                                  borderRadius: 6,
                                  border: "1px solid #1890ff",
                                  background: "#f0f5ff",
                                  color: "#1890ff",
                                  marginBottom: 6,
                                  fontSize: 13,
                                  padding: "4px 12px",
                                }}
                              >
                                {getToolLabel(tool)}
                              </Tag>
                            ))
                          ) : (
                            <Text style={{ color: "#8c8c8c" }}>
                              No ticket tools selected
                            </Text>
                          )}
                        </Space>

                        <Text
                          strong
                          style={{
                            fontSize: 14,
                            fontWeight: 600,
                            color: "#262626",
                            display: "block",
                            marginBottom: 4,
                          }}
                        >
                          Booking Tools
                        </Text>
                        <Space wrap>
                          {businessInfo?.booking_tools?.length > 0 ? (
                            businessInfo.booking_tools.map((tool) => (
                              <Tag
                                key={tool}
                                style={{
                                  borderRadius: 6,
                                  border: "1px solid #52c41a",
                                  background: "#f6ffed",
                                  color: "#52c41a",
                                  marginBottom: 6,
                                  fontSize: 13,
                                  padding: "4px 12px",
                                }}
                              >
                                {getToolLabel(tool)}
                              </Tag>
                            ))
                          ) : (
                            <Text style={{ color: "#8c8c8c" }}>
                              No booking tools selected
                            </Text>
                          )}
                        </Space>
                      </div>
                    </div>

                    {/* Required Information Fields */}
                    <div
                      style={{
                        marginBottom: 10,
                        display: "flex",
                        alignItems: "flex-start",
                        gap: 12,
                      }}
                    >
                      <div
                        style={{
                          width: 6,
                          height: 6,
                          borderRadius: "50%",
                          background: "#262626",
                          marginTop: 6,
                          flexShrink: 0,
                        }}
                      />
                      <div style={{ flex: 1 }}>
                        <Text
                          strong
                          style={{
                            fontSize: 14,
                            fontWeight: 600,
                            color: "#262626",
                            display: "block",
                            marginBottom: 4,
                          }}
                        >
                          Ticket Required Information
                        </Text>
                        <Space wrap style={{ marginBottom: 12 }}>
                          {businessInfo?.ticket_required_info?.filter(field => !['_issue_type', '_description'].includes(field))?.length > 0 ? (
                            businessInfo.ticket_required_info.filter(field => !['_issue_type', '_description'].includes(field)).map((field) => (
                              <Tag
                                key={field}
                                style={{
                                  borderRadius: 6,
                                  border: "1px solid #1890ff",
                                  background: "#f0f5ff",
                                  color: "#1890ff",
                                  marginBottom: 6,
                                  fontSize: 13,
                                  padding: "4px 12px",
                                }}
                              >
                                {field}
                              </Tag>
                            ))
                          ) : (
                            <Text style={{ color: "#8c8c8c" }}>
                              No ticket required fields configured
                            </Text>
                          )}
                        </Space>

                        <Text
                          strong
                          style={{
                            fontSize: 14,
                            fontWeight: 600,
                            color: "#262626",
                            display: "block",
                            marginBottom: 4,
                          }}
                        >
                          Booking Required Information
                        </Text>
                        <Space wrap>
                          {businessInfo?.booking_required_info?.length > 0 ? (
                            businessInfo.booking_required_info.map((field) => (
                              <Tag
                                key={field}
                                style={{
                                  borderRadius: 6,
                                  border: "1px solid #52c41a",
                                  background: "#f6ffed",
                                  color: "#52c41a",
                                  marginBottom: 6,
                                  fontSize: 13,
                                  padding: "4px 12px",
                                }}
                              >
                                {field}
                              </Tag>
                            ))
                          ) : (
                            <Text style={{ color: "#8c8c8c" }}>
                              No booking required fields configured
                            </Text>
                          )}
                        </Space>
                      </div>
                    </div>

                    {/* AI Greeting Configuration */}
                    <div
                      style={{
                        marginBottom: 10,
                        display: "flex",
                        alignItems: "flex-start",
                        gap: 12,
                      }}
                    >
                      <div
                        style={{
                          width: 6,
                          height: 6,
                          borderRadius: "50%",
                          background: "#262626",
                          marginTop: 6,
                          flexShrink: 0,
                        }}
                      />
                      <div style={{ flex: 1 }}>
                        <Text
                          strong
                          style={{
                            fontSize: 14,
                            fontWeight: 600,
                            color: "#262626",
                            display: "block",
                            marginBottom: 4,
                          }}
                        >
                          Initial Greeting
                        </Text>
                        <div>
                          <Tag
                            style={{
                              borderRadius: 6,
                              border: businessInfo?.AI_greeting
                                ? "1px solid #1890ff"
                                : "1px solid #d9d9d9",
                              background: businessInfo?.AI_greeting
                                ? "#f0f5ff"
                                : "#fafafa",
                              color: businessInfo?.AI_greeting
                                ? "#1890ff"
                                : "#595959",
                              fontSize: 13,
                              padding: "4px 12px",
                            }}
                          >
                            {businessInfo?.AI_greeting ? "Friendly Greeting" : "Direct Response"}
                          </Tag>
                          <div style={{ marginTop: 6 }}>
                            <Text style={{ fontSize: 13, color: "#8c8c8c" }}>
                              {businessInfo?.AI_greeting
                                ? "AI introduces itself with a warm greeting"
                                : "AI provides direct responses without greetings"
                              }
                            </Text>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Ticket Required Information */}
                    <div
                      style={{
                        marginBottom: 0,
                        display: "flex",
                        alignItems: "flex-start",
                        gap: 12,
                      }}
                    >
                      <div
                        style={{
                          width: 6,
                          height: 6,
                          borderRadius: "50%",
                          background: "#262626",
                          marginTop: 6,
                          flexShrink: 0,
                        }}
                      />
                      <div style={{ flex: 1 }}>
                        <Text
                          strong
                          style={{
                            fontSize: 14,
                            fontWeight: 600,
                            color: "#262626",
                            display: "block",
                            marginBottom: 4,
                          }}
                        >
                          Required Ticket Information
                        </Text>
                        <Space wrap>
                          {businessInfo?.ticket_required_info?.length > 0 ? (
                            businessInfo.ticket_required_info
                              .filter(field => !["_issue_type", "_description"].includes(field))
                              .map((field) => (
                                <Tag
                                  key={field}
                                  style={{
                                    borderRadius: 6,
                                    border: "1px solid #d9d9d9",
                                    background: "#fafafa",
                                    marginBottom: 6,
                                    fontSize: 13,
                                    padding: "4px 12px",
                                  }}
                                >
                                  {field}
                                </Tag>
                              ))
                          ) : (
                            <Text style={{ color: "#8c8c8c" }}>
                              No additional fields required
                            </Text>
                          )}
                        </Space>
                        {businessInfo?.ticket_required_info?.length > 0 && (
                          <div style={{ marginTop: 6 }}>
                            <Text style={{ fontSize: 13, color: "#8c8c8c" }}>
                              Information customers must provide when creating tickets
                            </Text>
                          </div>
                        )}
                      </div>
                    </div>
                  </Space>
                </div>
              </Card>
            </Col>

            {/* Setup Summary */}
            <Col xs={24} lg={12}>
              <Card
                title={
                  <Space>
                    <CheckCircleOutlined
                      style={{ color: "#595959", fontSize: 16 }}
                    />
                    <Text strong style={{ fontSize: 16 }}>
                      Setup Summary
                    </Text>
                  </Space>
                }
                className="summary-card"
                style={{
                  height: "100%",
                  borderRadius: "8px",
                  border: "1px solid #f0f0f0",
                }}
              >
                <div style={{ padding: "20px" }}>
                  <Space
                    direction="vertical"
                    size={8}
                    style={{ width: "100%" }}
                  >
                    <div
                      style={{
                        marginBottom: 10,
                        display: "flex",
                        alignItems: "flex-start",
                        gap: 12,
                      }}
                    >
                      <div
                        style={{
                          width: 6,
                          height: 6,
                          borderRadius: "50%",
                          background: "#262626",
                          marginTop: 6,
                          flexShrink: 0,
                        }}
                      />
                      <div style={{ flex: 1 }}>
                        <Text
                          strong
                          style={{
                            fontSize: 14,
                            fontWeight: 600,
                            color: "#262626",
                            display: "block",
                            marginBottom: 4,
                          }}
                        >
                          Setup Status
                        </Text>
                        <Tag
                          style={{
                            borderRadius: 6,
                            border: "1px solid #52c41a",
                            background: "#f6ffed",
                            color: "#52c41a",
                            fontSize: 13,
                            padding: "4px 12px",
                          }}
                        >
                          <iconify-icon
                            icon="mingcute:check-2-line"
                            width="14"
                            height="14"
                            style={{ marginRight: 4 }}
                          />
                          Ready for Completion
                        </Tag>
                      </div>
                    </div>

                    <div
                      style={{
                        marginBottom: 10,
                        display: "flex",
                        alignItems: "flex-start",
                        gap: 12,
                      }}
                    >
                      <div
                        style={{
                          width: 6,
                          height: 6,
                          borderRadius: "50%",
                          background: "#262626",
                          marginTop: 6,
                          flexShrink: 0,
                        }}
                      />
                      <div style={{ flex: 1 }}>
                        <Text
                          strong
                          style={{
                            fontSize: 14,
                            fontWeight: 600,
                            color: "#262626",
                            display: "block",
                            marginBottom: 4,
                          }}
                        >
                          Configuration Progress
                        </Text>
                        <Space direction="vertical" size={4}>
                          <div
                            style={{
                              display: "flex",
                              alignItems: "center",
                              gap: 8,
                            }}
                          >
                            <iconify-icon
                              icon="mingcute:check-2-line"
                              width="16"
                              height="16"
                              style={{ color: "#52c41a" }}
                            />
                            <Text style={{ fontSize: 14, color: "#595959" }}>
                              Organization Information Complete
                            </Text>
                          </div>
                          <div
                            style={{
                              display: "flex",
                              alignItems: "center",
                              gap: 8,
                            }}
                          >
                            <iconify-icon
                              icon="mingcute:check-2-line"
                              width="16"
                              height="16"
                              style={{ color: "#52c41a" }}
                            />
                            <Text style={{ fontSize: 14, color: "#595959" }}>
                              AI Agent Configuration Complete
                            </Text>
                          </div>
                          <div
                            style={{
                              display: "flex",
                              alignItems: "center",
                              gap: 8,
                            }}
                          >
                            <iconify-icon
                              icon="mingcute:check-2-line"
                              width="16"
                              height="16"
                              style={{ color: "#52c41a" }}
                            />
                            <Text style={{ fontSize: 14, color: "#595959" }}>
                              Ticket & Booking Tools Configured
                            </Text>
                          </div>
                          <div
                            style={{
                              display: "flex",
                              alignItems: "center",
                              gap: 8,
                            }}
                          >
                            <iconify-icon
                              icon="mingcute:check-2-line"
                              width="16"
                              height="16"
                              style={{ color: "#52c41a" }}
                            />
                            <Text style={{ fontSize: 14, color: "#595959" }}>
                              Required Information Fields Set
                            </Text>
                          </div>
                        </Space>
                      </div>
                    </div>

                    {businessInfo?.updated_at && (
                      <div
                        style={{
                          marginBottom: 0,
                          display: "flex",
                          alignItems: "flex-start",
                          gap: 12,
                        }}
                      >
                        <div
                          style={{
                            width: 6,
                            height: 6,
                            borderRadius: "50%",
                            background: "#262626",
                            marginTop: 6,
                            flexShrink: 0,
                          }}
                        />
                        <div style={{ flex: 1 }}>
                          <Text
                            strong
                            style={{
                              fontSize: 14,
                              fontWeight: 600,
                              color: "#262626",
                              display: "block",
                              marginBottom: 4,
                            }}
                          >
                            Last Updated
                          </Text>
                          <Text style={{ fontSize: 14, color: "#595959" }}>
                            {new Date(businessInfo.updated_at).toLocaleString()}
                          </Text>
                        </div>
                      </div>
                    )}
                  </Space>
                </div>
              </Card>
            </Col>
          </Row>

          {/* Success Message */}
          <div
            style={{
              marginTop: 32,
              textAlign: "center",
              background: "#dcfce7",
              padding: "24px",
              borderRadius: "8px",
              border: "1px solid #f0f0f0",
            }}
          >
            <div style={{ marginBottom: 8 }}>
              <Text style={{ fontSize: 16, fontWeight: 500, color: "#262626" }}>
                🎉 Setup Complete!
              </Text>
            </div>
            <div>
              <Text type="secondary" style={{ fontSize: 14, lineHeight: 1.6 }}>
                Your AI assistant is ready to help your customers.
              </Text>
              <div
                style={{
                  marginTop: 12,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  gap: 4,
                }}
              >
                <Text style={{ fontSize: 13, color: "#6b7280" }}>
                  You can update these settings anytime in
                </Text>
                <div style={{ display: "flex", alignItems: "center", gap: 6 }}>
                  <Text
                    style={{
                      background: "#fff",
                      padding: "4px 8px",
                      borderRadius: 6,
                      fontSize: 13,
                      fontWeight: 500,
                      color: "#374151",
                      border: "1px solid #e5e7eb",
                      boxShadow: "0 1px 2px rgba(0, 0, 0, 0.05)",
                    }}
                  >
                    Settings
                  </Text>
                  <Text
                    style={{ fontSize: 13, color: "#9ca3af", fontWeight: 500 }}
                  >
                    →
                  </Text>
                  <Text
                    style={{
                      background: "#fff",
                      padding: "4px 8px",
                      borderRadius: 6,
                      fontSize: 13,
                      fontWeight: 500,
                      color: "#374151",
                      border: "1px solid #e5e7eb",
                      boxShadow: "0 1px 2px rgba(0, 0, 0, 0.05)",
                    }}
                  >
                    AI Setup
                  </Text>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default Stage3Completion;
