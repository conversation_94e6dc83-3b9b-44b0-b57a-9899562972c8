import React, { useState, useEffect } from "react";
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Typography,
  Space,
  Divider,
  Row,
  Col,
  message,
  Skeleton,
  Switch,
  Tag,
  Tooltip,
} from "antd";
import { BankOutlined, BulbOutlined, SaveOutlined, PlusOutlined, SettingOutlined, CheckOutlined } from "@ant-design/icons";
import PhoneInput from "react-phone-input-2";
import {
  isPossiblePhoneNumber,
  parsePhoneNumber,
} from "react-phone-number-input";
import "react-phone-input-2/lib/style.css";
import { useAppDispatch } from "../../../hooks/reduxHooks";
import {
  getBusinessInfoApi,
  getTenantLanguageListApi,
  getTenantOrgTypesApi,
  getTenantToolsApi,
  editBusinessInfoApi,
} from "../../../services/tenantSetup.service";
import AISettingsComponent from "./AISettingsComponent";
// import "./AISetupComponent.scss";

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option, OptGroup } = Select;

const AISetupComponent = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(true);
  const [dataLoaded, setDataLoaded] = useState({
    businessInfo: false,
    orgTypes: false,
    languages: false,
    tools: false
  });
  const [savingOrgInfo, setSavingOrgInfo] = useState(false);
  const [savingAgentSetup, setSavingAgentSetup] = useState(false);
  const [savingCtaConfig, setSavingCtaConfig] = useState(false);
  const [savingAiConfig, setSavingAiConfig] = useState(false);
  const [phoneError, setPhoneError] = useState("");
  const [organizationTypes, setOrganizationTypes] = useState([]);
  const [languages, setLanguages] = useState([]);
  const [toolsList, setToolsList] = useState([]);
  const [bookingList, setBookingList] = useState([]);
  const [phoneInputValue, setPhoneInputValue] = useState("");
  const [primaryLanguage, setPrimaryLanguage] = useState(null);
  const dispatch = useAppDispatch();

  // Watch for organization type changes
  const selectedOrgType = Form.useWatch("org_type", form);

  // Utility function to parse phone number and extract country code
  const parsePhoneNumberData = (phoneNumber) => {
    if (!phoneNumber) {
      return {
        phone_input: "",
        country_code: "",
        phone_number: "",
      };
    }

    try {
      // If the phone number doesn't start with +, add it
      const formattedPhone = phoneNumber.startsWith("+")
        ? phoneNumber
        : `+${phoneNumber}`;

      // Parse the phone number
      const parsed = parsePhoneNumber(formattedPhone);

      if (parsed && parsed.country && parsed.nationalNumber) {
        return {
          phone_input: phoneNumber, // Keep original format for PhoneInput
          country_code: `+${parsed.countryCallingCode}`,
          phone_number: parsed.nationalNumber,
        };
      }
    } catch (error) {
      console.log("Error parsing phone number:", error);
    }

    // Fallback - return the original number as phone_input
    return {
      phone_input: phoneNumber,
      country_code: "",
      phone_number: phoneNumber.replace(/^\+/, ""), // Remove + if present for fallback
    };
  };

  // Fetch organization types, languages, and tools
  useEffect(() => {
    (async () => {
      // Fetch Organization Types
      dispatch(
        getTenantOrgTypesApi({
          finalCallback: () => {},
          successCallback: (response) => {
            console.log("Organization Types:", response);
            setOrganizationTypes(response);
            setDataLoaded(prev => ({ ...prev, orgTypes: true }));
          },
          failureCallback: () => {},
        })
      );

      // Fetch Languages
      dispatch(
        getTenantLanguageListApi({
          finalCallback: () => {},
          successCallback: (response) => {
            console.log("Languages:", response);
            setLanguages(response);
            setDataLoaded(prev => ({ ...prev, languages: true }));
          },
          failureCallback: () => {},
        })
      );

      // Fetch Tools (CTA options)
      dispatch(
        getTenantToolsApi({
          finalCallback: () => {},
          successCallback: (response) => {
            console.log("Tools API Response:", response);
            // Separate tools and booking items
            if (response.tools) {
              console.log("Setting tools list:", response.tools);
              setToolsList(response.tools);
            }
            if (response.booking) {
              console.log("Setting booking list:", response.booking);
              setBookingList(response.booking);
            }
            // Fallback for old API format
            if (Array.isArray(response) && !response.tools && !response.booking) {
              console.log("Using fallback format for tools:", response);
              setToolsList(response);
            }
            setDataLoaded(prev => ({ ...prev, tools: true }));
          },
          failureCallback: () => {},
        })
      );
    })();
  }, [dispatch]);

  // Update loading state when all data is loaded
  useEffect(() => {
    console.log("Data loaded status:", dataLoaded);
    const allDataLoaded = Object.values(dataLoaded).every(loaded => loaded);
    console.log("All data loaded:", allDataLoaded);
    if (allDataLoaded) {
      console.log("Setting loading to false");
      setLoading(false);
    }
  }, [dataLoaded]);

  // Auto-set agent goal type when organization type changes (only if no goal is already set)
  useEffect(() => {
    if (!selectedOrgType || !organizationTypes.length) return;

    const selectedType = organizationTypes.find(
      (t) => t.name === selectedOrgType
    );

    if (!selectedType?.agent_goals?.length) return;

    // Only set default if no agent goal is currently selected
    const currentGoal = form.getFieldValue("agent_goal_type");
    if (!currentGoal) {
      const defaultGoal = selectedType.agent_goals[0];
      form.setFieldValue("agent_goal_type", defaultGoal.prompt_type);
    }
  }, [organizationTypes, selectedOrgType, form]);

  // Function to populate form from API response
  const populateFormFromApi = (apiData) => {
    if (!apiData) return;

    // Parse phone number if it exists
    let phoneData = {};
    if (apiData.org_contact) {
      const phoneNumber = apiData.org_contact.toString();
      phoneData = parsePhoneNumberData(phoneNumber);
      setPhoneInputValue(phoneNumber);
    }

    // Map API response to form fields
    const formData = {
      // Organization Information
      org_name: apiData.org_name || "",
      org_type: apiData.org_type || "",
      org_description: apiData.org_description || "",
      org_email: apiData.org_email || "",
      ...phoneData,

      // Agent Setup
      agent_name: apiData.agent_name || "AI Bot",
      agent_goal_type: apiData.agent_goal_type || apiData.agent_goal || "",
      additional_agent_goal: apiData.additional_agent_goal || "",
      language: apiData.language || [],

      // Tools and Response Style
      used_tools: apiData.used_tools || [],
      booking: apiData.booking || [],
      selectedResponseStyle: apiData.preferred_prompt || "",

      // AI Configuration
      AI_greeting: apiData.AI_greeting !== undefined ? apiData.AI_greeting : false,
      ticket_required_info: apiData.ticket_required_info || [],
      booking_required_info: apiData.booking_required_info || [],
    };

    // Set form values
    console.log("Setting form data:", formData);
    form.setFieldsValue(formData);

    // Set primary language (first language in the array is considered primary)
    const languageArray = apiData.language || [];
    if (languageArray.length > 0 && languageArray[0] !== "Auto Detect") {
      setPrimaryLanguage(languageArray[0]);
    }
  };

  // Load business info from API
  useEffect(() => {
    (() => {
      dispatch(
        getBusinessInfoApi({
          finalCallback: () => {},
          successCallback: (response) => {
            console.log("Business Info Response from ai settings:", response);
            populateFormFromApi(response);
            setDataLoaded(prev => ({ ...prev, businessInfo: true }));
          },
          failureCallback: () => {},
        })
      );
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dispatch, form]);

  // Ensure agent goal is properly selected when both business info and org types are loaded
  useEffect(() => {
    const currentOrgType = form.getFieldValue("org_type");
    const currentAgentGoal = form.getFieldValue("agent_goal_type");

    if (!currentOrgType || !currentAgentGoal || !organizationTypes.length) return;

    // Find the organization type
    const selectedType = organizationTypes.find(
      (t) => t.name === currentOrgType
    );

    if (!selectedType?.agent_goals?.length) return;

    // Check if the current agent goal is valid for the selected org type
    const isValidGoal = selectedType.agent_goals.some(
      (goal) => goal.prompt_type === currentAgentGoal
    );

    // If the current goal is not valid for this org type, reset to first available goal
    if (!isValidGoal) {
      const defaultGoal = selectedType.agent_goals[0];
      form.setFieldValue("agent_goal_type", defaultGoal.prompt_type);
    }
  }, [organizationTypes, form]);

  const handleSave = async (values) => {
    // Validate phone number - now required
    if (!values.phone_input) {
      setPhoneError("Please enter a phone number");
      return;
    }

    const phoneToValidate = values.phone_input.startsWith("+")
      ? values.phone_input
      : `+${values.phone_input}`;

    const isPhoneNumberValid = isPossiblePhoneNumber(phoneToValidate);

    if (!isPhoneNumberValid) {
      setPhoneError("Please enter a valid phone number");
      return;
    }

    setPhoneError("");

    try {
      // Prepare API data in the format expected by editBusinessInfoApi
      const apiData = {
        org_name: values.org_name || "",
        org_type: values.org_type || "",
        org_description: values.org_description || "",
        org_contact: values.phone_input || "",
        org_email: values.org_email || "",
        org_goal: values.org_description || "", // Using org_description as org_goal
        agent_name: values.agent_name || "AI Bot",
        agent_goal_type: values.agent_goal_type || "",
        additional_agent_goal: values.additional_agent_goal || "",
        language: (() => {
          const selectedLanguages = values.language || [];
          if (!primaryLanguage || !selectedLanguages.includes(primaryLanguage)) {
            return selectedLanguages;
          }
          // Place primary language first
          const otherLanguages = selectedLanguages.filter(lang => lang !== primaryLanguage);
          return [primaryLanguage, ...otherLanguages];
        })(),
        preferred_prompt: values.selectedResponseStyle || "",
        used_tools: values.used_tools || [],
        booking: values.booking || [],
        AI_greeting: values.AI_greeting !== undefined ? values.AI_greeting : false,
        ticket_required_info: (() => {
          const ticketInfo = values.ticket_required_info || [];
          const hiddenFields = ["issue_type", "description"];

          // Remove any existing hidden fields to prevent duplicates
          const cleanTicketInfo = ticketInfo.filter(field => !hiddenFields.includes(field));

          return [
            ...cleanTicketInfo,
            ...hiddenFields
          ];
        })(),
        booking_required_info: values.booking_required_info || [],
      };

      console.log("Saving AI setup data:", apiData);

      // Return a Promise that resolves when the API call completes
      return new Promise((resolve, reject) => {
        dispatch(
          editBusinessInfoApi({
            params: apiData,
            finalCallback: () => resolve(),
            successCallback: (response) => {
              console.log("AI setup update success:", response);

              // Save to localStorage as backup
              const updatedData = {
                ...values,
                updatedAt: new Date().toISOString(),
              };
              localStorage.setItem("aiSetupData", JSON.stringify(updatedData));

              message.success("AI setup updated successfully!");
            },
            failureCallback: (error) => {
              console.error("AI setup update error:", error);
              message.error("Failed to save AI setup. Please try again.");
              reject(error);
            },
          })
        );
      });
    } catch (error) {
      console.error("Error saving setup data:", error);
      message.error("Failed to save setup data");
      throw error;
    }
  };

  // Individual save functions for each section
  const handleSaveOrgInfo = async () => {
    setSavingOrgInfo(true);
    try {
      await handleSave(form.getFieldsValue());
    } catch (error) {
      // Error handling is already done in handleSave
    } finally {
      setSavingOrgInfo(false);
    }
  };

  const handleSaveAgentSetup = async () => {
    setSavingAgentSetup(true);
    try {
      await handleSave(form.getFieldsValue());
    } catch (error) {
      // Error handling is already done in handleSave
    } finally {
      setSavingAgentSetup(false);
    }
  };

  const handleSaveCtaConfig = async () => {
    setSavingCtaConfig(true);
    try {
      await handleSave(form.getFieldsValue());
    } catch (error) {
      // Error handling is already done in handleSave
    } finally {
      setSavingCtaConfig(false);
    }
  };

  const handleSaveAiConfig = async () => {
    setSavingAiConfig(true);
    try {
      await handleSave(form.getFieldsValue());
    } catch (error) {
      // Error handling is already done in handleSave
    } finally {
      setSavingAiConfig(false);
    }
  };

  return (
    <div className="ai-setup-component">
      <Card
        style={{
          width: "100%",
          borderRadius: 6,
          boxShadow: "0 1px 4px rgba(0, 0, 0, 0.05)",
        }}
        bodyStyle={{
          padding: 24,
          width: "100%",
        }}
      >
        <Skeleton loading={loading} active paragraph={{ rows: 8 }}>
          <div
            style={{ display: "flex", alignItems: "center", marginBottom: 8 }}
          >
            <Title
              level={4}
              style={{ margin: 0, fontWeight: 600 }}
            >
              AI Setup Configuration
            </Title>
          </div>

          <Text type="secondary" style={{ display: "block", marginBottom: 24 }}>
            Manage your AI assistant configuration and business information
          </Text>

          <Divider style={{ margin: "0 0 24px 0" }} />

          <Form
            form={form}
            layout="vertical"
            onFinish={handleSave}
            style={{ width: "100%" }}
          >
            {/* Organization Information Section */}
            <Card
              title={
                <Space>
                  <BankOutlined style={{ color: "#1890ff" }} />
                  <Text strong style={{ fontSize: 16}}>Organization Information</Text>
                </Space>
              }
              style={{ marginBottom: 24, border: "1px solid #f0f0f0" }}
              bodyStyle={{
                padding: "20px",
              }}
            >
              <Row gutter={[16, 0]}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="org_name"
                    label="Organization Name"
                    required
                    rules={[
                      {
                        required: true,
                        message: "Please enter organization name",
                      },
                    ]}
                  >
                    <Input placeholder="Enter your organization name" />
                  </Form.Item>
                </Col>

                <Col xs={24} sm={12}>
                  <Form.Item
                    name="org_type"
                    label="Organization Type"
                    required
                    rules={[
                      {
                        required: true,
                        message: "Please select organization type",
                      },
                    ]}
                  >
                    <Select
                      placeholder="Select organization type"
                      style={{ width: "100%" }}
                    >
                      {organizationTypes.map((orgType) => (
                        <Option key={orgType.name} value={orgType.name}>
                          {orgType.label}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>

                <Col xs={24}>
                  <Form.Item
                    name="org_description"
                    label="Organization Description"
                    required
                    rules={[
                      {
                        required: true,
                        message: "Please enter organization description",
                      },
                    ]}
                  >
                    <TextArea
                      rows={3}
                      placeholder="Describe your organization..."
                    />
                  </Form.Item>
                </Col>

                <Col xs={24} sm={12}>
                  <Form.Item
                    name="phone_input"
                    label="Organization Contact Number"
                    required
                    validateStatus={phoneError ? "error" : ""}
                    help={phoneError || ""}
                    rules={[
                      {
                        required: true,
                        message: "Please enter organization contact number",
                      },
                    ]}
                  >
                    <PhoneInput
                      country={"np"}
                      enableSearch={true}
                      inputClass="form-input"
                      containerClass="phone-input-container"
                      buttonClass="country-dropdown"
                      searchClass="country-search"
                      dropdownClass="country-dropdown-list"
                      value={
                        phoneInputValue ||
                        form.getFieldValue("phone_input") ||
                        ""
                      }
                      inputStyle={{
                        width: "100%",
                        height: "40px",
                        fontSize: "14px",
                        fontWeight: "400",
                        borderRadius: "0 8px 8px 0",
                      }}
                      buttonStyle={{
                        borderRadius: "8px 0 0 8px",
                        borderRight: "none",
                        backgroundColor: "#fafafa",
                      }}
                      onChange={(phone, countryData) => {
                        setPhoneError("");
                        setPhoneInputValue(phone);
                        const countryCode = countryData.dialCode
                          ? `+${countryData.dialCode}`
                          : "";
                        const fullNumber = phone;
                        const phoneNumberOnly = countryData.dialCode
                          ? fullNumber.replace(countryData.dialCode, "")
                          : fullNumber;
                        const cleanedPhoneNumber = phoneNumberOnly.replace(
                          /\D/g,
                          ""
                        );

                        form.setFieldsValue({
                          country_code: countryCode,
                          phone_number: cleanedPhoneNumber,
                          phone_input: phone,
                        });
                      }}
                      preferredCountries={["in", "us", "np", "gb"]}
                      autoFormat={true}
                      placeholder="Enter organization contact number"
                      disableSearchIcon={false}
                      searchPlaceholder="Search country"
                    />
                  </Form.Item>

                  {/* Hidden fields for country code and phone number */}
                  <Form.Item name="country_code" hidden={true}>
                    <Input />
                  </Form.Item>

                  <Form.Item name="phone_number" hidden={true}>
                    <Input />
                  </Form.Item>
                </Col>

                <Col xs={24} sm={12}>
                  <Form.Item
                    name="org_email"
                    label="Organization Email"
                    required
                    rules={[
                      {
                        type: "email",
                        message: "Please enter a valid email address",
                      },
                      {
                        required: true,
                        message: "Please enter organization email",
                      },
                    ]}
                  >
                    <Input
                      type="email"
                      placeholder="Enter your organization email"
                    />
                  </Form.Item>
                </Col>
              </Row>

              {/* Organization Info Save Button */}
              <div style={{ textAlign: "right", marginTop: 16, paddingTop: 16, borderTop: "1px solid #f0f0f0" }}>
                <Button
                  type="primary"
                  onClick={handleSaveOrgInfo}
                  loading={savingOrgInfo}
                  icon={<SaveOutlined />}
                  style={{
                    borderRadius: "6px",
                    height: "36px",
                    paddingLeft: "20px",
                    paddingRight: "20px",
                    fontSize: "14px",
                    fontWeight: "500",
                  }}
                >
                  Save Organization Info
                </Button>
              </div>
            </Card>

            {/* AI Agent Setup Section */}
            <Card
              title={
                <Space>
                  <BulbOutlined style={{ color: "#52c41a" }} />
                  <Text strong style={{ fontSize: 16}}>AI Agent Configuration</Text>
                </Space>
              }
              style={{ marginBottom: 24, border: "1px solid #f0f0f0" }}
              bodyStyle={{ padding: "20px" }}
            >
              <Row gutter={[16, 0]}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="agent_name"
                    label="Agent Name"
                    required
                    rules={[
                      {
                        required: true,
                        message: "Please enter the agent name",
                      },
                    ]}
                  >
                    <Input placeholder="Enter agent name" />
                  </Form.Item>
                </Col>

                <Col xs={24} sm={12}>
                  <Form.Item
                    name="language"
                    label="Language"
                    rules={[
                      { required: true, message: "Please select languages" },
                    ]}
                  >
                    <Select
                      mode="multiple"
                      placeholder="Select languages"
                      style={{ width: "100%" }}
                      value={form.getFieldValue("language") || []}
                      onChange={(selected) => {
                        if (
                          selected.includes("Auto Detect") &&
                          selected.length > 1
                        ) {
                          if (selected[selected.length - 1] === "Auto Detect") {
                            form.setFieldsValue({ language: ["Auto Detect"] });
                            setPrimaryLanguage(null);
                          } else {
                            const filteredSelected = selected.filter(
                              (v) => v !== "Auto Detect"
                            );
                            form.setFieldsValue({ language: filteredSelected });
                            // If primary was removed, reset it
                            if (!filteredSelected.includes(primaryLanguage)) {
                              setPrimaryLanguage(filteredSelected[0] || null);
                            }
                          }
                        } else {
                          form.setFieldsValue({ language: selected });
                          // If primary was removed, reset it
                          if (!selected.includes(primaryLanguage)) {
                            setPrimaryLanguage(selected[0] || null);
                          }
                        }
                      }}
                      showSearch
                      optionFilterProp="children"
                      dropdownStyle={{ minWidth: 200 }}
                      tagRender={(props) => {
                        const { label, value, closable, onClose } = props;
                        const selectedLanguages = form.getFieldValue("language") || [];
                        const isPrimary = value === primaryLanguage;
                        const showPrimaryOption = selectedLanguages.length > 1 &&
                                                value !== "Auto Detect" &&
                                                selectedLanguages.includes(value);

                        return (
                          <Tag
                            color={isPrimary ? 'blue' : 'default'}
                            closable={closable}
                            onClose={onClose}
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              marginRight: 4,
                              marginBottom: 4,
                              marginTop: 4,
                            }}
                          >
                            {label}
                            {showPrimaryOption && !isPrimary && (
                              <Tooltip title="The primary language will be used to respond when the user sends a message in a language other than the selected ones.">
                                <Button
                                  size="small"
                                  type="link"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    setPrimaryLanguage(value);
                                  }}
                                  onMouseDown={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                  }}
                                  style={{
                                    marginLeft: 8,
                                    fontSize: 12,
                                    color: '#1890ff',
                                    cursor: 'pointer',
                                  }}
                                >
                                  Set Primary
                                </Button>
                              </Tooltip>
                            )}
                            {isPrimary && <CheckOutlined style={{ marginLeft: 8 }} />}
                          </Tag>
                        );
                      }}
                    >
                      <OptGroup label="Smart Options">
                        <Option value="Auto Detect">🌐 Auto Detect</Option>
                      </OptGroup>
                      <OptGroup label="Languages">
                        {languages.map((lang) => (
                          <Option key={lang} value={lang}>
                            {lang}
                          </Option>
                        ))}
                      </OptGroup>
                    </Select>
                  </Form.Item>
                </Col>

                <Col xs={24}>
                  <Form.Item
                    name="agent_goal_type"
                    label={<span>Agent Goal</span>}
                    rules={[
                      {
                        required: true,
                        message: "Please select an agent goal type",
                      },
                    ]}
                  >
                    <Select
                      placeholder="Select agent goal type"
                      style={{ width: "100%" }}
                    >
                      {selectedOrgType &&
                        organizationTypes
                          .find((t) => t.name === selectedOrgType)
                          ?.agent_goals?.map((goal) => (
                            <Option
                              key={goal.prompt_type}
                              value={goal.prompt_type}
                            >
                              {goal.prompt_summary}
                            </Option>
                          ))}
                    </Select>
                  </Form.Item>
                </Col>

                <Col xs={24}>
                  <Form.Item
                    name="additional_agent_goal"
                    label="Additional Agent Instructions"
                  >
                    <TextArea
                      rows={3}
                      placeholder="Describe any additional goals or behaviors for your AI agent..."
                    />
                  </Form.Item>
                </Col>
              </Row>

              {/* AI Agent Setup Save Button */}
              <div style={{ textAlign: "right", marginTop: 16, paddingTop: 16, borderTop: "1px solid #f0f0f0" }}>
                <Button
                  type="primary"
                  onClick={handleSaveAgentSetup}
                  loading={savingAgentSetup}
                  icon={<SaveOutlined />}
                  style={{
                    borderRadius: "6px",
                    height: "36px",
                    paddingLeft: "20px",
                    paddingRight: "20px",
                    fontSize: "14px",
                    fontWeight: "500",
                  }}
                >
                  Save AI Agent Configuration
                </Button>
              </div>
            </Card>

            {/* AI Configuration Section */}
            <Card
              title={
                <Space>
                  <SettingOutlined style={{ color: "#1890ff" }} />
                  <Text strong style={{ fontSize: 16}}>AI Configuration</Text>
                </Space>
              }
              style={{ marginBottom: 24, border: "1px solid #f0f0f0" }}
              bodyStyle={{ padding: "20px" }}
            >
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="selectedResponseStyle"
                    label="AI Response Style"
                    rules={[
                      {
                        required: true,
                        message: "Please select response style",
                      },
                    ]}
                  >
                    <Select placeholder="Select AI response style">
                      <Option value="simplified">
                        Simplified - Clear, concise answers
                      </Option>
                      <Option value="standard">
                        Standard - Comprehensive explanations
                      </Option>
                      <Option value="elaborated">
                        Elaborated - Technical details with step-by-step
                        guidance
                      </Option>
                    </Select>
                  </Form.Item>
                </Col>

                <Col xs={24}>
                  <Form.Item
                    name="AI_greeting"
                    label="Initial AI Greeting"
                    rules={[
                      {
                        required: true,
                        message: "Please select initial greeting option",
                      },
                    ]}
                  >
                    <AIGreetingToggle />
                  </Form.Item>
                </Col>
              </Row>

              {/* AI Configuration Save Button */}
              <div style={{ textAlign: "right", marginTop: 16, paddingTop: 16, borderTop: "1px solid #f0f0f0" }}>
                <Button
                  type="primary"
                  onClick={handleSaveAiConfig}
                  loading={savingAiConfig}
                  icon={<SaveOutlined />}
                  style={{
                    borderRadius: "6px",
                    height: "36px",
                    paddingLeft: "20px",
                    paddingRight: "20px",
                    fontSize: "14px",
                    fontWeight: "500",
                  }}
                >
                  Save AI Configuration
                </Button>
              </div>
            </Card>


            {/* AI Settings Section */}
            <Card
              title={
                <Space>
                  <SettingOutlined style={{ color: "#722ed1" }} />
                  <Text strong style={{ fontSize: 16}}>AI Agent Settings</Text>
                </Space>
              }
              style={{ marginBottom: 24, border: "1px solid #f0f0f0" }}
              bodyStyle={{ padding: "20px" }}
            >
              <AISettingsComponent />
            </Card>

            {/* Call to Action Configuration Section */}
            <Card
              title={
                <Space>
                  <SettingOutlined style={{ color: "#52c41a" }} />
                  <Text strong style={{ fontSize: 16}}>Call to Action Configuration</Text>
                </Space>
              }
              style={{ marginBottom: 24, border: "1px solid #f0f0f0" }}
              bodyStyle={{ padding: "20px" }}
            >
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="used_tools"
                    label="Ticket Tools"
                    rules={[
                      {
                        required: true,
                        message: "Please select tools",
                      },
                    ]}
                  >
                    <Select
                      mode="multiple"
                      placeholder="Select ticket tools"
                      style={{ width: "100%" }}
                      optionLabelProp="label"
                      value={form.getFieldValue("used_tools") || []}
                    >
                      {toolsList && toolsList.length > 0 && toolsList.map((tool) => (
                        <Option
                          key={tool.name}
                          value={tool.name}
                          label={tool.label}
                          title={tool.description}
                        >
                          <div>
                            <div style={{ fontWeight: 500 }}>{tool.label}</div>
                            <div style={{ fontSize: "12px", color: "#8c8c8c" }}>
                              {tool.description}
                            </div>
                          </div>
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>

                <Col xs={24} sm={12}>
                  <Form.Item
                    name="booking"
                    label="Booking Tools"
                    required
                    rules={[
                      {
                        required: true,
                        message: "Please select booking tools",
                      },
                    ]}
                  >
                    <Select
                      mode="multiple"
                      placeholder="Select booking tools"
                      style={{ width: "100%" }}
                      optionLabelProp="label"
                      value={form.getFieldValue("booking") || []}
                    >
                      {bookingList && bookingList.length > 0 && bookingList.map((booking) => (
                        <Option
                          key={booking.name}
                          value={booking.name}
                          label={booking.label}
                          title={booking.description}
                        >
                          <div>
                            <div style={{ fontWeight: 500 }}>{booking.label}</div>
                            <div style={{ fontSize: "12px", color: "#8c8c8c" }}>
                              {booking.description}
                            </div>
                          </div>
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>


                                <Col xs={24} sm={12}>
                  <Form.Item
                    name="ticket_required_info"
                    label="Required Ticket Information"
                    rules={[
                      {
                        required: true,
                        message: "Please configure ticket information fields",
                      },
                    ]}
                  >
                    <TicketInfoChipInput />
                  </Form.Item>
                </Col>

                <Col xs={24} sm={12}>
                  <Form.Item
                    name="booking_required_info"
                    label="Required Booking Information"
                    required
                    rules={[
                      {
                        required: true,
                        message: "Please configure booking information fields",
                      },
                    ]}
                  >
                    <BookingInfoChipInput />
                  </Form.Item>
                </Col>
              </Row>

              {/* CTA Configuration Save Button */}
              <div style={{ textAlign: "right", marginTop: 16, paddingTop: 16, borderTop: "1px solid #f0f0f0" }}>
                <Button
                  type="primary"
                  onClick={handleSaveCtaConfig}
                  loading={savingCtaConfig}
                  icon={<SaveOutlined />}
                  style={{
                    borderRadius: "6px",
                    height: "36px",
                    paddingLeft: "20px",
                    paddingRight: "20px",
                    fontSize: "14px",
                    fontWeight: "500",
                  }}
                >
                  Save CTA Configuration
                </Button>
              </div>
            </Card>
          </Form>
        </Skeleton>
      </Card>
    </div>
  );
};

// AI Greeting Toggle Component
const AIGreetingToggle = ({ value, onChange }) => {
  const handleChange = (checked) => {
    onChange?.(checked);
  };

  return (
    <div style={{ width: "100%" }}>
      {/* Toggle Options */}
      <div style={{ display: "flex", gap: "12px", marginBottom: 16 }}>
        <div
          onClick={() => handleChange(true)}
          style={{
            flex: 1,
            padding: "16px 20px",
            border: value === true ? "2px solid #1890ff" : "1px solid #d9d9d9",
            borderRadius: "8px",
            cursor: "pointer",
            backgroundColor: value === true ? "#f0f5ff" : "#fff",
            transition: "all 0.2s ease",
            textAlign: "center"
          }}
        >
          <div style={{ display: "flex", alignItems: "center", justifyContent: "center", marginBottom: 8 }}>
            <div
              style={{
                width: 18,
                height: 18,
                borderRadius: "50%",
                border: "2px solid",
                borderColor: value === true ? "#1890ff" : "#d9d9d9",
                backgroundColor: value === true ? "#1890ff" : "transparent",
                marginRight: 8,
                display: "flex",
                alignItems: "center",
                justifyContent: "center"
              }}
            >
              {value === true && (
                <div style={{ width: 8, height: 8, borderRadius: "50%", backgroundColor: "white" }} />
              )}
            </div>
            <Text strong style={{ color: value === true ? "#1890ff" : "#8c8c8c", fontSize: 15 }}>
              Friendly Greeting
            </Text>
          </div>
          <Text style={{ fontSize: 13, color: value === true ? "#666" : "#8c8c8c" }}>
            AI introduces itself warmly
          </Text>
        </div>

        <div
          onClick={() => handleChange(false)}
          style={{
            flex: 1,
            padding: "16px 20px",
            border: value === false ? "2px solid #1890ff" : "1px solid #d9d9d9",
            borderRadius: "8px",
            cursor: "pointer",
            backgroundColor: value === false ? "#f0f5ff" : "#fff",
            transition: "all 0.2s ease",
            textAlign: "center"
          }}
        >
          <div style={{ display: "flex", alignItems: "center", justifyContent: "center", marginBottom: 8 }}>
            <div
              style={{
                width: 18,
                height: 18,
                borderRadius: "50%",
                border: "2px solid",
                borderColor: value === false ? "#1890ff" : "#d9d9d9",
                backgroundColor: value === false ? "#1890ff" : "transparent",
                marginRight: 8,
                display: "flex",
                alignItems: "center",
                justifyContent: "center"
              }}
            >
              {value === false && (
                <div style={{ width: 8, height: 8, borderRadius: "50%", backgroundColor: "white" }} />
              )}
            </div>
            <Text strong style={{ color: value === false ? "#1890ff" : "#8c8c8c", fontSize: 15 }}>
              Direct Response
            </Text>
          </div>
          <Text style={{ fontSize: 13, color: value === false ? "#666" : "#8c8c8c" }}>
            AI responds directly
          </Text>
        </div>
      </div>

      {/* Example Preview */}
      <div style={{
        padding: "16px",
        backgroundColor: "#fafafa",
        borderRadius: "8px",
        border: "1px solid #f0f0f0"
      }}>
        <Text style={{ fontSize: 13, color: "#8c8c8c", marginBottom: 12, display: "block", fontWeight: 600 }}>
          Example Response:
        </Text>

        <div style={{ marginBottom: 10 }}>
          <Text style={{ fontSize: 13, color: "#666" }}>
            <strong>Customer:</strong> Hello!
          </Text>
        </div>

        <div>
          <Text style={{ fontSize: 13, color: value ? "#1890ff" : "#666", fontWeight: 500 }}>
            <strong>AI:</strong> {value
              ? "Hello! I'm your AI support assistant. How can I assist you today?"
              : "Hi! How can I assist you today?"
            }
          </Text>
        </div>
      </div>
    </div>
  );
};

// Ticket Info Chip Input Component
const TicketInfoChipInput = ({ value = [], onChange }) => {
  const [inputValue, setInputValue] = useState("");
  const [inputVisible, setInputVisible] = useState(false);

  const suggestions = [
    "Name",
    "Contact Number",
    "Email",
    "Address"
  ];

  // Filter out hidden fields for UI display
  const hiddenFields = ["issue_type", "description"];
  const visibleValue = value.filter(item => !hiddenFields.includes(item));

  const handleInputConfirm = () => {
    const trimmedValue = inputValue.trim();
    if (trimmedValue && !visibleValue.includes(trimmedValue)) {
      // Keep hidden fields and add new visible field
      const hiddenFieldsInValue = value.filter(item => hiddenFields.includes(item));
      const newValue = [...visibleValue, trimmedValue, ...hiddenFieldsInValue];
      onChange?.(newValue);
    }
    setInputValue("");
    setInputVisible(false);
  };

  const handleTagClose = (removedTag) => {
    // Remove only from visible fields, keep hidden fields
    const hiddenFieldsInValue = value.filter(item => hiddenFields.includes(item));
    const newVisibleValue = visibleValue.filter((tag) => tag !== removedTag);
    const newValue = [...newVisibleValue, ...hiddenFieldsInValue];
    onChange?.(newValue);
  };

  const handleSuggestionClick = (suggestion) => {
    if (!visibleValue.includes(suggestion)) {
      // Keep hidden fields and add new visible field
      const hiddenFieldsInValue = value.filter(item => hiddenFields.includes(item));
      const newValue = [...visibleValue, suggestion, ...hiddenFieldsInValue];
      onChange?.(newValue);
    }
  };

  const showInput = () => {
    setInputVisible(true);
  };

  const hideInput = () => {
    setInputValue("");
    setInputVisible(false);
  };

  // Calculate available suggestions based on visible value only
  const availableSuggestions = suggestions.filter(suggestion => !visibleValue.includes(suggestion));

  return (
    <div>
      {/* Selected Fields */}
      <div style={{ marginBottom: 16 }}>
        {visibleValue.map((tag, index) => (
          <Tag
            key={`selected-${tag}-${index}`}
            closable
            onClose={() => handleTagClose(tag)}
            style={{
              marginBottom: 8,
              marginRight: 8,
              padding: "4px 8px",
              fontSize: "13px",
              borderRadius: "6px"
            }}
            color="blue"
          >
            {tag}
          </Tag>
        ))}
        {inputVisible ? (
          <Input
            type="text"
            size="small"
            style={{
              width: 200,
              marginBottom: 8,
              borderRadius: "6px"
            }}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onBlur={handleInputConfirm}
            onPressEnter={handleInputConfirm}
            onKeyDown={(e) => {
              if (e.key === 'Escape') {
                hideInput();
              }
            }}
            placeholder="Enter custom field name"
            autoFocus
          />
        ) : (
          <Tag
            onClick={showInput}
            style={{
              background: "#fff",
              borderStyle: "dashed",
              cursor: "pointer",
              marginBottom: 8,
              padding: "4px 12px",
              fontSize: "13px",
              borderRadius: "6px",
              color: "#1890ff"
            }}
          >
            <PlusOutlined /> Add Custom Field
          </Tag>
        )}
      </div>

      {/* Suggestions */}
      {availableSuggestions.length > 0 && (
        <div>
          <Text strong style={{ fontSize: 13, color: "#666", marginBottom: 8, display: "block" }}>
            Quick Add Suggestions:
          </Text>
          <div style={{ display: "flex", flexWrap: "wrap", gap: "6px" }}>
            {availableSuggestions.map((suggestion) => (
              <Tag
                key={`suggestion-${suggestion}`}
                onClick={() => handleSuggestionClick(suggestion)}
                style={{
                  cursor: "pointer",
                  backgroundColor: "#f0f0f0",
                  border: "1px solid #d9d9d9",
                  borderRadius: "4px",
                  fontSize: "12px",
                  padding: "2px 8px",
                  transition: "all 0.2s"
                }}
                className="suggestion-tag"
              >
                + {suggestion}
              </Tag>
            ))}
          </div>
        </div>
      )}

      {/* Description */}
      <div style={{ marginTop: 16, padding: 12, backgroundColor: "#f6f6f6", borderRadius: 6 }}>
        <Text style={{ fontSize: 12, color: "#666" }}>
          <strong>What this does:</strong> When customers create tickets or use call-to-action features, they'll be asked to provide this information.
          Choose what details will help your team assist them better and resolve their requests efficiently.
        </Text>
      </div>
    </div>
  );
};

// Booking Info Chip Input Component
const BookingInfoChipInput = ({ value = [], onChange }) => {
  const [inputValue, setInputValue] = useState("");
  const [inputVisible, setInputVisible] = useState(false);

  const suggestions = [
    "Name",
    "Contact Number",
    "Email",
    "Address",
  ];

  // No hidden fields for booking, all values are visible
  const visibleValue = value || [];

  const handleInputConfirm = () => {
    const trimmedValue = inputValue.trim();
    if (trimmedValue && !visibleValue.includes(trimmedValue)) {
      const newValue = [...visibleValue, trimmedValue];
      onChange?.(newValue);
    }
    setInputValue("");
    setInputVisible(false);
  };

  const handleTagClose = (removedTag) => {
    const newValue = visibleValue.filter((tag) => tag !== removedTag);
    onChange?.(newValue);
  };

  const handleSuggestionClick = (suggestion) => {
    if (!visibleValue.includes(suggestion)) {
      const newValue = [...visibleValue, suggestion];
      onChange?.(newValue);
    }
  };

  const showInput = () => {
    setInputVisible(true);
  };

  const hideInput = () => {
    setInputValue("");
    setInputVisible(false);
  };

  // Calculate available suggestions based on visible value only
  const availableSuggestions = suggestions.filter(suggestion => !visibleValue.includes(suggestion));

  return (
    <div>
      {/* Selected Fields */}
      <div style={{ marginBottom: 16 }}>
        {visibleValue.map((tag, index) => (
          <Tag
            key={`selected-${tag}-${index}`}
            closable
            onClose={() => handleTagClose(tag)}
            style={{
              marginBottom: 8,
              marginRight: 8,
              padding: "4px 8px",
              fontSize: "13px",
              borderRadius: "6px"
            }}
            color="green"
          >
            {tag}
          </Tag>
        ))}
        {inputVisible ? (
          <Input
            type="text"
            size="small"
            style={{
              width: 200,
              marginBottom: 8,
              borderRadius: "6px"
            }}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onBlur={handleInputConfirm}
            onPressEnter={handleInputConfirm}
            onKeyDown={(e) => {
              if (e.key === 'Escape') {
                hideInput();
              }
            }}
            placeholder="Enter custom field name"
            autoFocus
          />
        ) : (
          <Tag
            onClick={showInput}
            style={{
              background: "#fff",
              borderStyle: "dashed",
              cursor: "pointer",
              marginBottom: 8,
              padding: "4px 12px",
              fontSize: "13px",
              borderRadius: "6px",
              color: "#52c41a"
            }}
          >
            <PlusOutlined /> Add Custom Field
          </Tag>
        )}
      </div>

      {/* Suggestions */}
      {availableSuggestions.length > 0 && (
        <div>
          <Text strong style={{ fontSize: 13, color: "#666", marginBottom: 8, display: "block" }}>
            Quick Add Suggestions:
          </Text>
          <div style={{ display: "flex", flexWrap: "wrap", gap: "6px" }}>
            {availableSuggestions.map((suggestion) => (
              <Tag
                key={`suggestion-${suggestion}`}
                onClick={() => handleSuggestionClick(suggestion)}
                style={{
                  cursor: "pointer",
                  backgroundColor: "#f0f0f0",
                  border: "1px solid #d9d9d9",
                  borderRadius: "4px",
                  fontSize: "12px",
                  padding: "2px 8px",
                  transition: "all 0.2s"
                }}
                className="suggestion-tag"
              >
                + {suggestion}
              </Tag>
            ))}
          </div>
        </div>
      )}

      {/* Description */}
      <div style={{ marginTop: 16, padding: 12, backgroundColor: "#f6f6f6", borderRadius: 6 }}>
        <Text style={{ fontSize: 12, color: "#666" }}>
          <strong>What this does:</strong> When customers make bookings or schedule services, they'll be asked to provide this information.
          Choose what details will help your team process bookings efficiently and provide better service.
        </Text>
      </div>
    </div>
  );
};

export default AISetupComponent;
